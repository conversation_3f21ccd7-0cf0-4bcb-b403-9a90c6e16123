* DONE
** properly store the password in a cache in the daemon while running
  - proper authentication in the cli would be good too
** change the search results to a table form for easier reading in CLI
  - search text should list name, shortened description, and address
  - list pods should list name, address, creation date, and modified date
  - search by subject should list all things found with a predicate:object pattern
  - all other searches should return the raw output
  - have a raw output option for search
** fix the graph get_my_pods function, doesn't return everything in the graphs for some reason
** related to the get_my_pods issue, the text SPARQL query only returns things that it found, I want to return all of the things about subjects it found
** need to change the colony app take/restore pattern to do the same thing that is done in colony-daemon to prevent errors from crashing everything
** need to fix colony for the list_my_pods command format
** remove the pod_refs directory creation, it isn't used anywhere now
** add the ability to create a configuration pod with all of the default graph data and anything else
  - then when starting up or doing a refresh, pull this down first, get all the pod addresses from here
    and download all of them
  - then go through and download any references that you don't yet have
  - would be much faster when starting from scratch
** add a 'rename pod' function
** implement address count predicate/value in the configuration pod
  - write it to the configuration graph each time a new key is created and delete the old value
** when modifying a pod, delete the old "modified" date entries each so they don't accumulate
** need a way to recycle unused pointer and empty scratchpad addresses
  - update code to mark them as unused/empty
    - during remove_pod and put_subject_data when scratchpads become empty
  - then when calling add_pod/put_subject_data/add_pod_ref, check for empty/unused addresses first before creating a new one
    - really just modify the create_scratchpad and create_pointer functions to look first to grab them
  - when recycling, need to keep track of what kind of key it was in order to reuse the object at that address
** add a 'remove pod' function
  - clears out the current pod data
  - marks the pointer/scratchpad address as UNUSED in configuration graph
  - when creating a new pointer or new scratchpads, look to the UNUSED addresses
  - when refreshing, need to walk through newly unused addresses
    - if an unused address is a pod pointer, delete the file from the disk and clear out that graph
    - if a newly unused adress is a scratchpad, delete that file from the disk
  - add a check to make sure the configuration pod cannot be removed
** need a function to map the pointer and scratchpad addresses to the proper key_store pointers/scratchpads/bad array during refresh
  - any addresses not found in the configuration graph after fetching it during a refresh should be marked bad
** add $SECRET_KEY as a default for the wallet key in colony-daemon
  - look in the ant CLI and steal the code where it uses the wallet if it exists, else use $SECRET_KEY
  - priority:
    - key given at the user prompt
    - $SECRET_KEY environment variable
    - ant CLI wallet    
** need to figure out why that 'e' character is there when entering the password in colony-daemon
  - this is a library problem in how it interacts with the terminal apparently
** remove the address analysis commands when refreshing
  - everything is in the configuration, we know what addresses are used and their types
  - massive performance improvement
  - need to:
    - download the configuration pod pointer first
      - if not found error encountered, return from refresh operation with warning saying there was nothing to be found on the network
      - check if it is newer than the version in cache, if not return from refresh operation, else download the configuration pod scratchpads
    - create a method to find the pointer and scratchpad addresses from the configuration pod
    - if pointer is marked unused or scratchpad is empty, check to make sure they are empty locally, if not, clear them out
    - serial option:
      - walk through the pointers and download, download scratchpads if newer than what's in cache (already done)

** change the list my pods results to a table form for easier reading in CLI
  - list pods should list name, address, creation date, and modified date
** remove the legacy endpoint stuff from the colonyd
** update colonylib to add a name to the configuration pod, no reason this shouldn't have a name like everything else
** create function to return the list of pod addresses to be updated
  - return JSON string with 'update/add' or 'remove'
** test multi pod ref structure that it still works
  - seems to be a bug in the refresh_ref because it doesn't seem to catch everything on the first
  - it catches everything in multiple passes, need to investigate more thoroughly
** test the various remove/rename scenarios and make sure it always works
** reupload the genesis pod with the latest schema on main net
** upload the colonyd and colony binaries to Autonomi and add them to the genesis pod
** create a multi-platform binary release process for colony
** add a remove wallet function to colonylib to go with the add/get wallet functions
** create a browse function for search that let's you scroll through everything
** add a set_wallet and add_wallet Tauri command
  - add wallet adds a wallet private key and a name
  - set wallet makes a wallet active by name
** add a dweb connector function Tauri command
  - add dweb binaries as a side car and call 'dweb serve' from rust in a Tauri command
  - add a Tauri command to call the REST API call to 'dweb open <ADDRESS>'
  - could change the Tauri commands to call colonyd in the exact same way and have a '--daemon' mode that basically just bails on the GUI stuff

** update colonylib to add a set_active_wallet function
  - will write an 'active_wallet.json' file with the name and address of the active wallet
  - colony will need to be updated to grab the active wallet from this file instead of keeping it in the store
    - on initialization set the active wallet
  - colonyd will need to be updated to grab the active wallet as well instead of hard coding it to main
    - on initialization, set the active wallet
** update colonyd to add the wallet API and autonomi public file API
  - set_active_wallet
  - get_active_wallet
  - add_wallet
  - remove_wallet
  - list_wallets
  - upload_public_file
    - returns address of file, ANT tokens used, and gas amount used
  - download_public_file
** write documentation for the uploader and downloader commands
   - change the names??
** add ability to to short options on the uploader and downloader
   - example: --port for -p
** implement the auto setup feature
   - when clicking finish:
     - automatically do a sync
       - have a selection button to shut this off if you don't want to sync in initial start up
     - if stuff populates, great, done
     - if nothing populates:
       - create a new pod called "default"
       - add the genesis pod reference
       - do a sync
** change the info window so that it shows all of the data, not just some
   - map each predicate to a key and each object to a value
   - have a button to show the raw JSON-LD representation of the data
** add a metrics indicator for search
   - report number of items returned based on the number of rows
   - report how long the search took to complete
   - display these metrics in a single line of text in italicized font centered below the search bar
** change the search table to include more information
   - search table column changes
     - remove the info icon and its column, it is no longer necessary
     - column 1 should be the existing download column (this is the 2nd column in today's program)
       - when the object in the row is actively downloading, change the download arrow to an animated spinner
       - if already downloaded, change the download arrow to a green check mark icon
       - if the type is a dweb website, change the download icon to a website type icon
       - if the type is a pod or unspecified disable the download and remove the icon in this column
       - clicking on this cell has the same behavior of the existing download button, no change
     - column 2 should be the 'name column:
       - change the displayed content of the existing 'name' column to use the object value stored in the 'alternateName'
         predicate if it exists, else display the 'name' predicate value
       - clicking on a cell brings up the metadata information modal, no change in this behavior
     - column 3 is a new 'description' column
       - displays the 'description' predicate value
       - the description column should streth and take up available space in the column mapping
       - clicking on a cell brings up the metadata information modal
     - column 4 is a new 'type' column
       - display the last word of the schema type string. For example, if the type is 'http://schema.org/Book', just display 'Book' here.
       - clicking on a cell brings up the metadata information modal
     - column 5 is 'size'
       - behavior is the same as now except that if the size is unknown, display nothing instead of the current 'Unknown'
       - clicking on a cell brings up the metadata information modal
     - column 6 is the existing 'address' column with the following changes
       - display first 5 chars and last 5 chars with '...' in between
       - the text here should be italicized instead of normal
       - clicking on the address cell will copy the full address value to the clipboard
         and also display a toast that says 'Address <ADDRESS> copied!' where <ADDRESS> is the full address
       - this column should always be to the far right size and default minimum size to display the 5 prefix chars, three '...', chars, and 5 suffix chars
** when writing pod metadata:
   - the default template should be updated on visibility with the things it auto populates like name and contentSize and type
** add a timestamp attribute to the header of each scratchpad
   - when dealing with the multi scratchpad error, check to see which header has the latest value
   - implement the new scratchpad error handling thing that anselme put together
** on the wallet page:
   - show the ANT and ETH balance information
** change the file metadata text box so that it shows the pod address and file address as shortened strings that when you click, copies to the clipboard
** on the downloads page, on click add the ability to open the file
** in the metadata window and the upload file window, add an option to point to an existing file on the network
  - enables users to add metadata to existing stuff
* TO TEST
** multi-thread refresh function
  - create a task for each pointer and kick off concurrently
    - download pointer
    - if newer, download first scratchpad
    - create tasks for all additional scratchpads and download them concurrently
      - or maybe just serially. How often are people going to have multiple scratchpads per pod, probably skip this one for now unless its easy
** multi-thread the refresh_ref function
  - Basically just need to spawn tasks for the download_referenced_pod() function call for each depth
** multi-thread upload_all function
  - spawn a task for each upload function call
** update the wallet functions to return the public and private key, not just the private key
** found an error in the pod refresh ref function:
  - if have a configuration where a chain of pods [a -> b -> c] -> [d -> e -> f] -> [g -> h -> i] -> a, the 2nd user does not pick up pod a,b,c from the i->a reference
** update the Colony documentation and README
** build test suite for creating pod references to make sure everything works

* TODO
** finish syncing project gutenberg
   - in the ~/documents/project_gutenberg directory run:
     rsync -av --del aleph.gutenberg.org::gutenberg-epub .

** change everything that says pod to folder and everything that says pod reference to link
*** TO TEST - need to make the upload process easier and more fool proof
  - go back to field entry based on type instead of templates?
** change the upload process so that metadata can be written up front
** add additional search options
  - filter by type in a search
    - not just @type, but also the data type
  - add an advanced mode where you can put in raw SPARQL queries
    - if invalid, throw an error
  - sort by the newest entries
    - would need to add a modified date or something in the colonylib to keep track of this
  - when clicking on a pod, pull up a dialog that says 'search pod'

** AI enhancement button for the pod metadata editing functionality
   -  would be good to have an AI enhancement button like we have in the ia_downloader
** remove the default key option from colonyd initialization
** update colony backend to use colonyd
  - load colonyd as a side car binary
  - map the tauri commands to the colonyd REST API calls
  - use a local instance of colonyd if it is running on the system on the same port
    - test by using a health call
** ability to add plugins?
  - unsure how this would plug into the GUI
  - would be easier with the colonyd backend though
      
** investigate upload failures
** make videos for colony ecosystem
   - setting up and using colony
   - setting up and using colonyd/colony CLI
   - using ia_downloader and colony_uploader scripts
** update the colony_uploader to use the upload_public_file api instead of autonomi functions
  - run tests on the local test net when uploading to make sure everything is good, should be simple with everything in colonyd
    
** update the colony and colonyd version pointers in the help window to automatically grab the cargo version like colony does

** Error encountered on main net:
- First time it failed, didn't spit out anything
- Tried rerunning and got this mess:
2025-07-02T01:52:11.911989Z ERROR ThreadId(04) autonomi::client::data_types::pointer: /cargo/registry/src/index.crates.io-1949cf8c6b5b557f/autonomi-0.5.0/src/client/data_types/pointer.rs:288: Failed to update pointer at address aaa518a2cf8260f6bebc769c16b8147ea215adf569696497b7fc1f250823d89a49990187e83fc0f9ae1cf3d44afb7dce to the network: Put verification failed: Peers have conflicting entries for this record: {PeerId("12D3KooWSuTm6wx2myt5BVY6JGXyBZ7eVhYdbeLAub66iKBA5wTV"): Record { key: Key(b"\0\xd5\xa4s\xf9\x10\x9d\xfe\xbd\xdeo\xf6H\x0b_\xee\xf1bX\xa7X\xb4\"9\xe1\xf8;\xd3w\xf9\xc7\xb8"), value: [145, 2, 148, 220, 0, 48, 204, 170, 204, 165, 24, 204, 162, 204, 207, 204, 130, 96, 204, 246, 204, 190, 204, 188, 118, 204, 156, 22, 204, 184, 20, 126, 204, 162, 21, 204, 173, 204, 245, 105, 105, 100, 204, 151, 204, 183, 204, 252, 31, 37, 8, 35, 204, 216, 204, 154, 73, 204, 153, 1, 204, 135, 204, 232, 63, 204, 192, 204, 249, 204, 174, 28, 204, 243, 204, 212, 74, 204, 251, 125, 204, 206, 1, 129, 177, 83, 99, 114, 97, 116, 99, 104, 112, 97, 100, 65, 100, 100, 114, 101, 115, 115, 220, 0, 48, 204, 178, 36, 204, 145, 24, 30, 204, 166, 54, 204, 143, 204, 229, 116, 29, 90, 19, 204, 255, 57, 204, 226, 204, 135, 29, 204, 161, 60, 204, 190, 204, 208, 204, 220, 204, 178, 204, 238, 119, 204, 195, 204, 165, 20, 103, 35, 204, 211, 204, 233, 87, 93, 204, 190, 60, 87, 204, 191, 124, 118, 204, 155, 48, 204, 200, 71, 204, 193, 204, 206, 7, 220, 0, 96, 204, 148, 53, 22, 204, 238, 54, 204, 203, 204, 142, 204, 162, 111, 204, 176, 57, 204, 224, 204, 205, 26, 204, 243, 91, 204, 167, 22, 204, 190, 98, 204, 245, 127, 204, 253, 204, 200, 204, 153, 77, 204, 234, 57, 204, 142, 2, 100, 204, 253, 48, 204, 199, 59, 204, 199, 204, 130, 204, 185, 204, 180, 33, 204, 164, 204, 198, 204, 185, 86, 40, 21, 204, 180, 31, 11, 204, 237, 13, 204, 201, 204, 187, 204, 181, 204, 146, 204, 172, 204, 142, 93, 204, 158, 57, 86, 25, 36, 204, 188, 72, 80, 96, 101, 26, 204, 225, 204, 239, 100, 40, 204, 176, 204, 166, 104, 19, 204, 199, 21, 204, 151, 204, 224, 204, 250, 204, 174, 79, 204, 234, 102, 83, 111, 46, 68, 204, 167, 204, 221, 8, 122, 204, 149, 204, 252], publisher: None, expires: None }, PeerId("12D3KooWLmfdTmmTtKDwcKey28sybA1Kh9SShwAjf84e8pnPsSZT"): Record { key: Key(b"\0\xd5\xa4s\xf9\x10\x9d\xfe\xbd\xdeo\xf6H\x0b_\xee\xf1bX\xa7X\xb4\"9\xe1\xf8;\xd3w\xf9\xc7\xb8"), value: [145, 2, 148, 220, 0, 48, 204, 170, 204, 165, 24, 204, 162, 204, 207, 204, 130, 96, 204, 246, 204, 190, 204, 188, 118, 204, 156, 22, 204, 184, 20, 126, 204, 162, 21, 204, 173, 204, 245, 105, 105, 100, 204, 151, 204, 183, 204, 252, 31, 37, 8, 35, 204, 216, 204, 154, 73, 204, 153, 1, 204, 135, 204, 232, 63, 204, 192, 204, 249, 204, 174, 28, 204, 243, 204, 212, 74, 204, 251, 125, 204, 206, 0, 129, 177, 83, 99, 114, 97, 116, 99, 104, 112, 97, 100, 65, 100, 100, 114, 101, 115, 115, 220, 0, 48, 204, 178, 36, 204, 145, 24, 30, 204, 166, 54, 204, 143, 204, 229, 116, 29, 90, 19, 204, 255, 57, 204, 226, 204, 135, 29, 204, 161, 60, 204, 190, 204, 208, 204, 220, 204, 178, 204, 238, 119, 204, 195, 204, 165, 20, 103, 35, 204, 211, 204, 233, 87, 93, 204, 190, 60, 87, 204, 191, 124, 118, 204, 155, 48, 204, 200, 71, 204, 193, 204, 206, 7, 220, 0, 96, 204, 177, 204, 217, 204, 141, 204, 180, 204, 243, 36, 204, 136, 204, 150, 204, 251, 8, 204, 151, 204, 203, 204, 157, 204, 165, 64, 204, 159, 58, 1, 204, 198, 49, 122, 204, 253, 108, 58, 11, 126, 29, 83, 204, 164, 204, 189, 61, 65, 204, 150, 204, 166, 4, 93, 87, 204, 191, 106, 204, 193, 23, 102, 25, 204, 139, 204, 163, 33, 95, 204, 171, 15, 204, 179, 204, 185, 204, 156, 114, 204, 134, 204, 214, 204, 221, 20, 77, 19, 204, 152, 56, 204, 156, 79, 204, 215, 7, 114, 126, 204, 171, 73, 88, 34, 204, 233, 38, 26, 14, 65, 204, 150, 204, 236, 118, 35, 53, 23, 22, 65, 204, 233, 120, 204, 233, 81, 90, 204, 232, 119, 9, 107, 31, 204, 131, 9], publisher: None, expires: None }}
2025-07-02T01:52:11.913580Z ERROR ThreadId(04) colonylib::pod: /cargo/registry/src/index.crates.io-1949cf8c6b5b557f/colonylib-0.4.3/src/pod.rs:1845: Error occurred: Pointer(PutError(Network { address: NetworkAddress::PointerAddress(aaa518a2cf8260f6bebc769c16b8147ea215adf569696497b7fc1f250823d89a49990187e83fc0f9ae1cf3d44afb7dce) - (acae9b20a0dc6d7da27aa34239d97ee231fd389b014eace164e0171e7fd28969), network_error: PutRecordVerification("Peers have conflicting entries for this record: {PeerId(\"12D3KooWSuTm6wx2myt5BVY6JGXyBZ7eVhYdbeLAub66iKBA5wTV\"): Record { key: Key(b\"\\0\\xd5\\xa4s\\xf9\\x10\\x9d\\xfe\\xbd\\xdeo\\xf6H\\x0b_\\xee\\xf1bX\\xa7X\\xb4\\\"9\\xe1\\xf8;\\xd3w\\xf9\\xc7\\xb8\"), value: [145, 2, 148, 220, 0, 48, 204, 170, 204, 165, 24, 204, 162, 204, 207, 204, 130, 96, 204, 246, 204, 190, 204, 188, 118, 204, 156, 22, 204, 184, 20, 126, 204, 162, 21, 204, 173, 204, 245, 105, 105, 100, 204, 151, 204, 183, 204, 252, 31, 37, 8, 35, 204, 216, 204, 154, 73, 204, 153, 1, 204, 135, 204, 232, 63, 204, 192, 204, 249, 204, 174, 28, 204, 243, 204, 212, 74, 204, 251, 125, 204, 206, 1, 129, 177, 83, 99, 114, 97, 116, 99, 104, 112, 97, 100, 65, 100, 100, 114, 101, 115, 115, 220, 0, 48, 204, 178, 36, 204, 145, 24, 30, 204, 166, 54, 204, 143, 204, 229, 116, 29, 90, 19, 204, 255, 57, 204, 226, 204, 135, 29, 204, 161, 60, 204, 190, 204, 208, 204, 220, 204, 178, 204, 238, 119, 204, 195, 204, 165, 20, 103, 35, 204, 211, 204, 233, 87, 93, 204, 190, 60, 87, 204, 191, 124, 118, 204, 155, 48, 204, 200, 71, 204, 193, 204, 206, 7, 220, 0, 96, 204, 148, 53, 22, 204, 238, 54, 204, 203, 204, 142, 204, 162, 111, 204, 176, 57, 204, 224, 204, 205, 26, 204, 243, 91, 204, 167, 22, 204, 190, 98, 204, 245, 127, 204, 253, 204, 200, 204, 153, 77, 204, 234, 57, 204, 142, 2, 100, 204, 253, 48, 204, 199, 59, 204, 199, 204, 130, 204, 185, 204, 180, 33, 204, 164, 204, 198, 204, 185, 86, 40, 21, 204, 180, 31, 11, 204, 237, 13, 204, 201, 204, 187, 204, 181, 204, 146, 204, 172, 204, 142, 93, 204, 158, 57, 86, 25, 36, 204, 188, 72, 80, 96, 101, 26, 204, 225, 204, 239, 100, 40, 204, 176, 204, 166, 104, 19, 204, 199, 21, 204, 151, 204, 224, 204, 250, 204, 174, 79, 204, 234, 102, 83, 111, 46, 68, 204, 167, 204, 221, 8, 122, 204, 149, 204, 252], publisher: None, expires: None }, PeerId(\"12D3KooWLmfdTmmTtKDwcKey28sybA1Kh9SShwAjf84e8pnPsSZT\"): Record { key: Key(b\"\\0\\xd5\\xa4s\\xf9\\x10\\x9d\\xfe\\xbd\\xdeo\\xf6H\\x0b_\\xee\\xf1bX\\xa7X\\xb4\\\"9\\xe1\\xf8;\\xd3w\\xf9\\xc7\\xb8\"), value: [145, 2, 148, 220, 0, 48, 204, 170, 204, 165, 24, 204, 162, 204, 207, 204, 130, 96, 204, 246, 204, 190, 204, 188, 118, 204, 156, 22, 204, 184, 20, 126, 204, 162, 21, 204, 173, 204, 245, 105, 105, 100, 204, 151, 204, 183, 204, 252, 31, 37, 8, 35, 204, 216, 204, 154, 73, 204, 153, 1, 204, 135, 204, 232, 63, 204, 192, 204, 249, 204, 174, 28, 204, 243, 204, 212, 74, 204, 251, 125, 204, 206, 0, 129, 177, 83, 99, 114, 97, 116, 99, 104, 112, 97, 100, 65, 100, 100, 114, 101, 115, 115, 220, 0, 48, 204, 178, 36, 204, 145, 24, 30, 204, 166, 54, 204, 143, 204, 229, 116, 29, 90, 19, 204, 255, 57, 204, 226, 204, 135, 29, 204, 161, 60, 204, 190, 204, 208, 204, 220, 204, 178, 204, 238, 119, 204, 195, 204, 165, 20, 103, 35, 204, 211, 204, 233, 87, 93, 204, 190, 60, 87, 204, 191, 124, 118, 204, 155, 48, 204, 200, 71, 204, 193, 204, 206, 7, 220, 0, 96, 204, 177, 204, 217, 204, 141, 204, 180, 204, 243, 36, 204, 136, 204, 150, 204, 251, 8, 204, 151, 204, 203, 204, 157, 204, 165, 64, 204, 159, 58, 1, 204, 198, 49, 122, 204, 253, 108, 58, 11, 126, 29, 83, 204, 164, 204, 189, 61, 65, 204, 150, 204, 166, 4, 93, 87, 204, 191, 106, 204, 193, 23, 102, 25, 204, 139, 204, 163, 33, 95, 204, 171, 15, 204, 179, 204, 185, 204, 156, 114, 204, 134, 204, 214, 204, 221, 20, 77, 19, 204, 152, 56, 204, 156, 79, 204, 215, 7, 114, 126, 204, 171, 73, 88, 34, 204, 233, 38, 26, 14, 65, 204, 150, 204, 236, 118, 35, 53, 23, 22, 65, 204, 233, 120, 204, 233, 81, 90, 204, 232, 119, 9, 107, 31, 204, 131, 9], publisher: None, expires: None }}"), payment: None }))
2025-07-02T01:52:30.027683Z ERROR ThreadId(03) autonomi::client::data_types::scratchpad: /cargo/registry/src/index.crates.io-1949cf8c6b5b557f/autonomi-0.5.0/src/client/data_types/scratchpad.rs:104: Got multiple conflicting scratchpads for Key(b"\xea\xb9\x8eY\xdc\x8ew\x01\x0c\xf95\x96?\x07sb\x92A\x9b\x91\x03\xd7\xb1/K\xc2\xa6\xafR\xc5\xbdw") with the latest version, returning the first one
2025-07-02T01:53:28.834378Z ERROR ThreadId(04) autonomi::client::data_types::pointer: /cargo/registry/src/index.crates.io-1949cf8c6b5b557f/autonomi-0.5.0/src/client/data_types/pointer.rs:288: Failed to update pointer at address aaa518a2cf8260f6bebc769c16b8147ea215adf569696497b7fc1f250823d89a49990187e83fc0f9ae1cf3d44afb7dce to the network: Put verification failed: Peers have conflicting entries for this record: {PeerId("12D3KooWSuTm6wx2myt5BVY6JGXyBZ7eVhYdbeLAub66iKBA5wTV"): Record { key: Key(b"\0\xd5\xa4s\xf9\x10\x9d\xfe\xbd\xdeo\xf6H\x0b_\xee\xf1bX\xa7X\xb4\"9\xe1\xf8;\xd3w\xf9\xc7\xb8"), value: [145, 2, 148, 220, 0, 48, 204, 170, 204, 165, 24, 204, 162, 204, 207, 204, 130, 96, 204, 246, 204, 190, 204, 188, 118, 204, 156, 22, 204, 184, 20, 126, 204, 162, 21, 204, 173, 204, 245, 105, 105, 100, 204, 151, 204, 183, 204, 252, 31, 37, 8, 35, 204, 216, 204, 154, 73, 204, 153, 1, 204, 135, 204, 232, 63, 204, 192, 204, 249, 204, 174, 28, 204, 243, 204, 212, 74, 204, 251, 125, 204, 206, 2, 129, 177, 83, 99, 114, 97, 116, 99, 104, 112, 97, 100, 65, 100, 100, 114, 101, 115, 115, 220, 0, 48, 204, 178, 36, 204, 145, 24, 30, 204, 166, 54, 204, 143, 204, 229, 116, 29, 90, 19, 204, 255, 57, 204, 226, 204, 135, 29, 204, 161, 60, 204, 190, 204, 208, 204, 220, 204, 178, 204, 238, 119, 204, 195, 204, 165, 20, 103, 35, 204, 211, 204, 233, 87, 93, 204, 190, 60, 87, 204, 191, 124, 118, 204, 155, 48, 204, 200, 71, 204, 193, 204, 206, 7, 220, 0, 96, 204, 150, 0, 98, 204, 200, 92, 20, 114, 22, 204, 166, 204, 183, 40, 204, 238, 49, 96, 41, 18, 59, 204, 169, 45, 70, 204, 150, 204, 246, 20, 106, 204, 190, 204, 211, 204, 222, 64, 114, 204, 136, 204, 154, 7, 204, 215, 36, 204, 162, 75, 75, 204, 199, 3, 204, 204, 204, 171, 115, 204, 252, 13, 109, 73, 25, 204, 154, 10, 21, 204, 149, 204, 252, 121, 204, 223, 96, 204, 207, 204, 242, 71, 204, 188, 8, 204, 208, 122, 99, 103, 28, 204, 138, 65, 204, 141, 59, 16, 13, 204, 222, 39, 204, 235, 72, 2, 204, 202, 51, 52, 204, 196, 204, 158, 204, 222, 103, 204, 162, 114, 204, 218, 204, 153, 102, 204, 158, 204, 235, 204, 252, 204, 152, 204, 148, 30, 204, 188, 204, 201], publisher: None, expires: None }, PeerId("12D3KooWLmfdTmmTtKDwcKey28sybA1Kh9SShwAjf84e8pnPsSZT"): Record { key: Key(b"\0\xd5\xa4s\xf9\x10\x9d\xfe\xbd\xdeo\xf6H\x0b_\xee\xf1bX\xa7X\xb4\"9\xe1\xf8;\xd3w\xf9\xc7\xb8"), value: [145, 2, 148, 220, 0, 48, 204, 170, 204, 165, 24, 204, 162, 204, 207, 204, 130, 96, 204, 246, 204, 190, 204, 188, 118, 204, 156, 22, 204, 184, 20, 126, 204, 162, 21, 204, 173, 204, 245, 105, 105, 100, 204, 151, 204, 183, 204, 252, 31, 37, 8, 35, 204, 216, 204, 154, 73, 204, 153, 1, 204, 135, 204, 232, 63, 204, 192, 204, 249, 204, 174, 28, 204, 243, 204, 212, 74, 204, 251, 125, 204, 206, 0, 129, 177, 83, 99, 114, 97, 116, 99, 104, 112, 97, 100, 65, 100, 100, 114, 101, 115, 115, 220, 0, 48, 204, 178, 36, 204, 145, 24, 30, 204, 166, 54, 204, 143, 204, 229, 116, 29, 90, 19, 204, 255, 57, 204, 226, 204, 135, 29, 204, 161, 60, 204, 190, 204, 208, 204, 220, 204, 178, 204, 238, 119, 204, 195, 204, 165, 20, 103, 35, 204, 211, 204, 233, 87, 93, 204, 190, 60, 87, 204, 191, 124, 118, 204, 155, 48, 204, 200, 71, 204, 193, 204, 206, 7, 220, 0, 96, 204, 177, 204, 217, 204, 141, 204, 180, 204, 243, 36, 204, 136, 204, 150, 204, 251, 8, 204, 151, 204, 203, 204, 157, 204, 165, 64, 204, 159, 58, 1, 204, 198, 49, 122, 204, 253, 108, 58, 11, 126, 29, 83, 204, 164, 204, 189, 61, 65, 204, 150, 204, 166, 4, 93, 87, 204, 191, 106, 204, 193, 23, 102, 25, 204, 139, 204, 163, 33, 95, 204, 171, 15, 204, 179, 204, 185, 204, 156, 114, 204, 134, 204, 214, 204, 221, 20, 77, 19, 204, 152, 56, 204, 156, 79, 204, 215, 7, 114, 126, 204, 171, 73, 88, 34, 204, 233, 38, 26, 14, 65, 204, 150, 204, 236, 118, 35, 53, 23, 22, 65, 204, 233, 120, 204, 233, 81, 90, 204, 232, 119, 9, 107, 31, 204, 131, 9], publisher: None, expires: None }}
2025-07-02T01:53:28.834501Z ERROR ThreadId(04) colonylib::pod: /cargo/registry/src/index.crates.io-1949cf8c6b5b557f/colonylib-0.4.3/src/pod.rs:1845: Error occurred: Pointer(PutError(Network { address: NetworkAddress::PointerAddress(aaa518a2cf8260f6bebc769c16b8147ea215adf569696497b7fc1f250823d89a49990187e83fc0f9ae1cf3d44afb7dce) - (acae9b20a0dc6d7da27aa34239d97ee231fd389b014eace164e0171e7fd28969), network_error: PutRecordVerification("Peers have conflicting entries for this record: {PeerId(\"12D3KooWSuTm6wx2myt5BVY6JGXyBZ7eVhYdbeLAub66iKBA5wTV\"): Record { key: Key(b\"\\0\\xd5\\xa4s\\xf9\\x10\\x9d\\xfe\\xbd\\xdeo\\xf6H\\x0b_\\xee\\xf1bX\\xa7X\\xb4\\\"9\\xe1\\xf8;\\xd3w\\xf9\\xc7\\xb8\"), value: [145, 2, 148, 220, 0, 48, 204, 170, 204, 165, 24, 204, 162, 204, 207, 204, 130, 96, 204, 246, 204, 190, 204, 188, 118, 204, 156, 22, 204, 184, 20, 126, 204, 162, 21, 204, 173, 204, 245, 105, 105, 100, 204, 151, 204, 183, 204, 252, 31, 37, 8, 35, 204, 216, 204, 154, 73, 204, 153, 1, 204, 135, 204, 232, 63, 204, 192, 204, 249, 204, 174, 28, 204, 243, 204, 212, 74, 204, 251, 125, 204, 206, 2, 129, 177, 83, 99, 114, 97, 116, 99, 104, 112, 97, 100, 65, 100, 100, 114, 101, 115, 115, 220, 0, 48, 204, 178, 36, 204, 145, 24, 30, 204, 166, 54, 204, 143, 204, 229, 116, 29, 90, 19, 204, 255, 57, 204, 226, 204, 135, 29, 204, 161, 60, 204, 190, 204, 208, 204, 220, 204, 178, 204, 238, 119, 204, 195, 204, 165, 20, 103, 35, 204, 211, 204, 233, 87, 93, 204, 190, 60, 87, 204, 191, 124, 118, 204, 155, 48, 204, 200, 71, 204, 193, 204, 206, 7, 220, 0, 96, 204, 150, 0, 98, 204, 200, 92, 20, 114, 22, 204, 166, 204, 183, 40, 204, 238, 49, 96, 41, 18, 59, 204, 169, 45, 70, 204, 150, 204, 246, 20, 106, 204, 190, 204, 211, 204, 222, 64, 114, 204, 136, 204, 154, 7, 204, 215, 36, 204, 162, 75, 75, 204, 199, 3, 204, 204, 204, 171, 115, 204, 252, 13, 109, 73, 25, 204, 154, 10, 21, 204, 149, 204, 252, 121, 204, 223, 96, 204, 207, 204, 242, 71, 204, 188, 8, 204, 208, 122, 99, 103, 28, 204, 138, 65, 204, 141, 59, 16, 13, 204, 222, 39, 204, 235, 72, 2, 204, 202, 51, 52, 204, 196, 204, 158, 204, 222, 103, 204, 162, 114, 204, 218, 204, 153, 102, 204, 158, 204, 235, 204, 252, 204, 152, 204, 148, 30, 204, 188, 204, 201], publisher: None, expires: None }, PeerId(\"12D3KooWLmfdTmmTtKDwcKey28sybA1Kh9SShwAjf84e8pnPsSZT\"): Record { key: Key(b\"\\0\\xd5\\xa4s\\xf9\\x10\\x9d\\xfe\\xbd\\xdeo\\xf6H\\x0b_\\xee\\xf1bX\\xa7X\\xb4\\\"9\\xe1\\xf8;\\xd3w\\xf9\\xc7\\xb8\"), value: [145, 2, 148, 220, 0, 48, 204, 170, 204, 165, 24, 204, 162, 204, 207, 204, 130, 96, 204, 246, 204, 190, 204, 188, 118, 204, 156, 22, 204, 184, 20, 126, 204, 162, 21, 204, 173, 204, 245, 105, 105, 100, 204, 151, 204, 183, 204, 252, 31, 37, 8, 35, 204, 216, 204, 154, 73, 204, 153, 1, 204, 135, 204, 232, 63, 204, 192, 204, 249, 204, 174, 28, 204, 243, 204, 212, 74, 204, 251, 125, 204, 206, 0, 129, 177, 83, 99, 114, 97, 116, 99, 104, 112, 97, 100, 65, 100, 100, 114, 101, 115, 115, 220, 0, 48, 204, 178, 36, 204, 145, 24, 30, 204, 166, 54, 204, 143, 204, 229, 116, 29, 90, 19, 204, 255, 57, 204, 226, 204, 135, 29, 204, 161, 60, 204, 190, 204, 208, 204, 220, 204, 178, 204, 238, 119, 204, 195, 204, 165, 20, 103, 35, 204, 211, 204, 233, 87, 93, 204, 190, 60, 87, 204, 191, 124, 118, 204, 155, 48, 204, 200, 71, 204, 193, 204, 206, 7, 220, 0, 96, 204, 177, 204, 217, 204, 141, 204, 180, 204, 243, 36, 204, 136, 204, 150, 204, 251, 8, 204, 151, 204, 203, 204, 157, 204, 165, 64, 204, 159, 58, 1, 204, 198, 49, 122, 204, 253, 108, 58, 11, 126, 29, 83, 204, 164, 204, 189, 61, 65, 204, 150, 204, 166, 4, 93, 87, 204, 191, 106, 204, 193, 23, 102, 25, 204, 139, 204, 163, 33, 95, 204, 171, 15, 204, 179, 204, 185, 204, 156, 114, 204, 134, 204, 214, 204, 221, 20, 77, 19, 204, 152, 56, 204, 156, 79, 204, 215, 7, 114, 126, 204, 171, 73, 88, 34, 204, 233, 38, 26, 14, 65, 204, 150, 204, 236, 118, 35, 53, 23, 22, 65, 204, 233, 120, 204, 233, 81, 90, 204, 232, 119, 9, 107, 31, 204, 131, 9], publisher: None, expires: None }}"), payment: None }))
- reran a third time and everything seemed to be cleared up

** add endpoint to return the list of pod addresses that will be updated, either added or removed
  - endpoint is added, need to update colony CLI to show the update when listing pods
** list pods still lists pod references
  - query looks correct, unsure why it lists them. need to debug more thoroughly here.
** better handling for multi client scenarios
  - when creating a new pod locally, follow the normal procedure
  - when uploading, if the creation fails because there is something already there:
    - write the current graph for that pod into a temporary area
    - call the refresh command to make sure nothing else is missing and get the latest list of addresses
      - will wipe out the old data in the graph
    - get new addresses past the address count value
    - replace all of the old addresses in the temp graph file and create a new graph in the database
    - do this for all newly added pods

** add a "filter" predicate to colonylib to allow users to block a downstream reference pod from downloading or a subject/pod from showing up in searches
  - this attribute should pass through to all downstream users
  - there should be a 'block' and 'unblock' object definition, the closest one wins
** Add error handling to the Autonomi operations

* Things todo later
** thumbnail thoughts (on hold)
   - rows should be 2 lines worth of text tall
     - if there is a thumbnail available, make the row 3 lines of text tall
   - search column changes
     - column 1 in the row should be an image thumbnail column
       - After a text search or browse search completes, check if each row has a 'http://schema.org/Image' predicate with an 'ant://' address as the object
         - if so silently download the thumbnail image file at this address to the location described below using the download_data Tauri command:
           - The thumbnail download should be written to the 'thumbnails' directory in the com.colony.gui directory. If this directory doesn't exist, create it
           - The name of the downloaded thumbnail file is the 'ant://' address without the 'ant://' prefix
         - before downloading a thumbnail, check to see if it already exists in the aforementioned 'thumbnails' directory
       - Once downloaded, update the search table to display these thumbnails in the thumbnail column
         - dynamically update the search table as these files populate
       - download the thumbnails in the row order so that they appear at the top of the search first
         - thread the thumbnail download operation so that the downloads occur concurrently
** add an abandon_pod function that permanently deletes everything
  - applies the ABANDONED attribute to the pointers and scratchpad addresses
    - basically the same as putting it into the 'bad' array
  - clears out the data in the graph
  - on upload, it will publish the data deletion just like remove_pod
** add the ability to change the cocoon password in colony-cli
** merge the colonyd with the main colony app to enable running headless
  - and allow client interactions over REST API
